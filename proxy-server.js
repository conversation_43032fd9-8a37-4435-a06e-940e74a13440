const express = require('express');
const cors = require('cors');

// Disable SSL verification for internal APIs
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

// Proxy endpoint for forwarding chat requests
app.post('/chat', async (req, res) => {
  const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
  try {
    console.log('Received request:', JSON.stringify(req.body, null, 2));
    
    const response = await fetch('https://epro-bot-test.int.electroluxprofessional.com/insecure-chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(req.body),
    });
    
    console.log('Backend response status:', response.status);
    const text = await response.text();
    console.log('Backend response text:', text);
    
    res.status(response.status).send(text);
  } catch (err) {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Proxy error', details: err.message });
  }
});

// Proxy endpoint for downloading documents
app.post('/document', async (req, res) => {
  const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
  try {
    console.log('Received request:', JSON.stringify(req.body, null, 2));
    
    const response = await fetch('https://epro-bot-test.int.electroluxprofessional.com/api/v1/documents', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(req.body),
    });

    // Set content-type and disposition for PDF
    res.setHeader('Content-Type', response.headers.get('content-type') || 'application/pdf');
    if (response.headers.get('content-disposition')) {
      res.setHeader('Content-Disposition', response.headers.get('content-disposition'));
    }

    // Stream the PDF data
    const buffer = await response.arrayBuffer();
    res.status(response.status).send(Buffer.from(buffer));
    
  } catch (err) {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Proxy error', details: err.message });
  }
});

app.listen(PORT, () => {
  console.log(`Proxy server listening on port ${PORT}`);
});
