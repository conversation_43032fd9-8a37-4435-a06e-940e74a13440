# Chat Widget

A simple chat widget to be embedded in web applications. It provides a customizable chat interface that can connect to a backend (e.g., a bot) and aims to be easy to integrate into any website.

## Features (Work in progress)

- Modern, responsive UI with smooth animations
- Easily embeddable via a single JS file
- Fullscreen and mobile-friendly
- Customizable styles (uses Tailwind CSS and Google Fonts)

## Files

- `chat-widget.js`: Main widget script (injects UI, handles logic)
- `chat-widget-old.js`: Previous version (for reference)
- `index.html`: Example usage
- `proxy-server.js`: Simple Node.js proxy server for API requests
- `package.json`: Project metadata and dependencies
- `README.md`: This file

## Quick Start

1. **Clone or Download** this repository.
2. **Install dependencies** (for the proxy server):
   ```powershell
   npm install
   ```
3. **Start the proxy server** (if using the provided backend):
   ```powershell
   node proxy-server.js
   ```
4. **Open `index.html`** in your browser to see the widget in action.

## Embedding the Widget

To embed the chat widget in your own site:

1. Copy `chat-widget.js` to your project.
2. Add the following to your HTML:
   ```html
   <script src="chat-widget.js"></script>
   ```
3. The widget will appear in the bottom-right corner by default.

## Configuration

You can customize the widget by editing the `CONFIG` object at the top of `chat-widget.js`:

```js
const CONFIG = {
  bot: 'CALL_CENTER_BOT',
  apiUrl: 'http://localhost:3001/',
  answerType: 'html',
};
```

- `bot`: Bot or agent identifier
- `apiUrl`: Backend endpoint for chat
- `answerType`: Message format (`html` or `text`)

## Customization

- **Styles:** The widget injects Tailwind CSS and Google Fonts. You can further customize styles in `chat-widget.js`.
